package com.logictrue.service;

import com.baomidou.mybatisplus.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 实体类注解解析器
 * 解析MyBatis Plus实体类的注解信息，用于动态创建表结构
 */
public class EntityAnnotationParser {
    private static final Logger logger = LoggerFactory.getLogger(EntityAnnotationParser.class);

    /**
     * 表信息
     */
    public static class TableInfo {
        private String tableName;
        private List<ColumnInfo> columns;
        private List<String> indexes;

        public TableInfo() {
            this.columns = new ArrayList<>();
            this.indexes = new ArrayList<>();
        }

        // Getters and Setters
        public String getTableName() { return tableName; }
        public void setTableName(String tableName) { this.tableName = tableName; }

        public List<ColumnInfo> getColumns() { return columns; }
        public void setColumns(List<ColumnInfo> columns) { this.columns = columns; }

        public List<String> getIndexes() { return indexes; }
        public void setIndexes(List<String> indexes) { this.indexes = indexes; }
    }

    /**
     * 列信息
     */
    public static class ColumnInfo {
        private String columnName;
        private String fieldName;
        private String dataType;
        private boolean isPrimaryKey;
        private boolean isAutoIncrement;
        private boolean isNotNull;
        private String defaultValue;
        private String comment;
        private boolean exist = true;

        // Getters and Setters
        public String getColumnName() { return columnName; }
        public void setColumnName(String columnName) { this.columnName = columnName; }

        public String getFieldName() { return fieldName; }
        public void setFieldName(String fieldName) { this.fieldName = fieldName; }

        public String getDataType() { return dataType; }
        public void setDataType(String dataType) { this.dataType = dataType; }

        public boolean isPrimaryKey() { return isPrimaryKey; }
        public void setPrimaryKey(boolean primaryKey) { isPrimaryKey = primaryKey; }

        public boolean isAutoIncrement() { return isAutoIncrement; }
        public void setAutoIncrement(boolean autoIncrement) { isAutoIncrement = autoIncrement; }

        public boolean isNotNull() { return isNotNull; }
        public void setNotNull(boolean notNull) { isNotNull = notNull; }

        public String getDefaultValue() { return defaultValue; }
        public void setDefaultValue(String defaultValue) { this.defaultValue = defaultValue; }

        public String getComment() { return comment; }
        public void setComment(String comment) { this.comment = comment; }

        public boolean isExist() { return exist; }
        public void setExist(boolean exist) { this.exist = exist; }
    }

    /**
     * 解析实体类注解信息
     */
    public static TableInfo parseEntity(Class<?> entityClass) {
        logger.info("开始解析实体类: {}", entityClass.getName());

        TableInfo tableInfo = new TableInfo();

        // 解析表名
        String tableName = parseTableName(entityClass);
        tableInfo.setTableName(tableName);

        // 解析字段信息
        List<ColumnInfo> columns = parseFields(entityClass);
        tableInfo.setColumns(columns);

        logger.info("实体类解析完成: 表名={}, 字段数={}", tableName, columns.size());
        return tableInfo;
    }

    /**
     * 解析表名
     */
    private static String parseTableName(Class<?> entityClass) {
        TableName tableNameAnnotation = entityClass.getAnnotation(TableName.class);
        if (tableNameAnnotation != null && !tableNameAnnotation.value().isEmpty()) {
            return tableNameAnnotation.value();
        }

        // 如果没有@TableName注解，使用类名转下划线格式
        return camelToUnderscore(entityClass.getSimpleName());
    }

    /**
     * 解析字段信息
     */
    private static List<ColumnInfo> parseFields(Class<?> entityClass) {
        List<ColumnInfo> columns = new ArrayList<>();

        Field[] fields = entityClass.getDeclaredFields();
        for (Field field : fields) {
            // 跳过静态字段和被@TableField(exist = false)标记的字段
            if (java.lang.reflect.Modifier.isStatic(field.getModifiers())) {
                continue;
            }

            TableField tableField = field.getAnnotation(TableField.class);
            if (tableField != null && !tableField.exist()) {
                continue;
            }

            ColumnInfo columnInfo = parseField(field);
            if (columnInfo != null) {
                columns.add(columnInfo);
            }
        }

        return columns;
    }

    /**
     * 解析单个字段
     */
    private static ColumnInfo parseField(Field field) {
        ColumnInfo columnInfo = new ColumnInfo();

        // 设置字段名
        columnInfo.setFieldName(field.getName());

        // 解析列名
        String columnName = parseColumnName(field);
        columnInfo.setColumnName(columnName);

        // 解析数据类型
        String dataType = parseDataType(field);
        columnInfo.setDataType(dataType);

        // 解析主键信息
        TableId tableId = field.getAnnotation(TableId.class);
        if (tableId != null) {
            columnInfo.setPrimaryKey(true);
            columnInfo.setAutoIncrement(tableId.type() == IdType.AUTO);
        }

        // 解析字段属性
        TableField tableField = field.getAnnotation(TableField.class);
        if (tableField != null) {
            if (!tableField.value().isEmpty()) {
                columnInfo.setColumnName(tableField.value());
            }
        }

        // 设置默认属性
        setDefaultProperties(columnInfo, field);

        return columnInfo;
    }

    /**
     * 解析列名
     */
    private static String parseColumnName(Field field) {
        TableField tableField = field.getAnnotation(TableField.class);
        if (tableField != null && !tableField.value().isEmpty()) {
            return tableField.value();
        }

        // 默认使用字段名转下划线格式
        return camelToUnderscore(field.getName());
    }

    /**
     * 解析数据类型
     */
    private static String parseDataType(Field field) {
        Class<?> fieldType = field.getType();

        // 基本数据类型映射
        if (fieldType == Long.class || fieldType == long.class) {
            return "INTEGER";
        } else if (fieldType == Integer.class || fieldType == int.class) {
            return "INTEGER";
        } else if (fieldType == String.class) {
            return "TEXT";
        } else if (fieldType == LocalDateTime.class) {
            return "TIMESTAMP";
        } else if (fieldType == Boolean.class || fieldType == boolean.class) {
            return "INTEGER"; // SQLite中布尔值用INTEGER存储
        } else if (fieldType == Double.class || fieldType == double.class) {
            return "REAL";
        } else if (fieldType == Float.class || fieldType == float.class) {
            return "REAL";
        } else {
            // 默认使用TEXT类型
            return "TEXT";
        }
    }

    /**
     * 设置默认属性
     */
    private static void setDefaultProperties(ColumnInfo columnInfo, Field field) {
        // 主键字段通常不为空
        if (columnInfo.isPrimaryKey()) {
            columnInfo.setNotNull(true);
        }

        // 根据字段名设置一些默认值
        String fieldName = field.getName().toLowerCase();
        if (fieldName.contains("time") && columnInfo.getDataType().equals("TIMESTAMP")) {
            if (fieldName.contains("create")) {
                columnInfo.setDefaultValue("CURRENT_TIMESTAMP");
            } else if (fieldName.contains("update")) {
                columnInfo.setDefaultValue("CURRENT_TIMESTAMP");
            }
        }

        // 设置注释
        columnInfo.setComment(generateComment(field.getName()));
    }

    /**
     * 生成字段注释
     */
    private static String generateComment(String fieldName) {
        // 简单的字段名到注释的映射
        switch (fieldName.toLowerCase()) {
            case "id": return "主键ID";
            case "createtime": return "创建时间";
            case "updatetime": return "更新时间";
            case "createby": return "创建人";
            case "updateby": return "更新人";
            case "remark": return "备注";
            default: return fieldName;
        }
    }

    /**
     * 驼峰命名转下划线
     */
    private static String camelToUnderscore(String camelCase) {
        if (camelCase == null || camelCase.isEmpty()) {
            return camelCase;
        }

        StringBuilder result = new StringBuilder();
        for (int i = 0; i < camelCase.length(); i++) {
            char c = camelCase.charAt(i);
            if (Character.isUpperCase(c)) {
                if (i > 0) {
                    result.append('_');
                }
                result.append(Character.toLowerCase(c));
            } else {
                result.append(c);
            }
        }

        return result.toString();
    }
}
