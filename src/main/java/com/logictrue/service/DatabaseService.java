package com.logictrue.service;

import com.logictrue.config.MyBatisPlusConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 数据库服务类
 * 使用MyBatis Plus实现SQLite数据库连接、表创建和分页查询功能
 */
public class DatabaseService {
    private static final Logger logger = LoggerFactory.getLogger(DatabaseService.class);

    private static final String DB_NAME = "data_iot.db";

    // Excel数据相关表名
    private static final String DEVICE_DETECTION_DATA_TABLE = "device_detection_data";
    private static final String DEVICE_DETECTION_BASIC_FIELD_TABLE = "device_detection_basic_field";
    private static final String DEVICE_DETECTION_TABLE_HEADER_TABLE = "device_detection_table_header";
    private static final String DEVICE_DETECTION_TABLE_DATA_TABLE = "device_detection_table_data";

    private static DatabaseService instance;
    private String dbPath;
    private MyBatisPlusConfig myBatisPlusConfig;

    private DatabaseService() {
        // 获取jar包同级目录
        String jarDir = getJarDirectory();
        this.dbPath = jarDir + File.separator + DB_NAME;
        initializeDatabase();
        // 初始化MyBatis Plus配置
        this.myBatisPlusConfig = MyBatisPlusConfig.getInstance();
    }

    /**
     * 获取单例实例
     */
    public static synchronized DatabaseService getInstance() {
        if (instance == null) {
            instance = new DatabaseService();
        }
        return instance;
    }

    /**
     * 获取jar包所在目录
     */
    private String getJarDirectory() {
        try {
            String jarPath = DatabaseService.class.getProtectionDomain()
                    .getCodeSource().getLocation().toURI().getPath();
            File jarFile = new File(jarPath);

            if (jarFile.isFile()) {
                // 运行的是jar包
                return jarFile.getParent();
            } else {
                // 开发环境，使用项目根目录
                return System.getProperty("user.dir");
            }
        } catch (Exception e) {
            logger.error("获取jar包目录失败", e);
            return System.getProperty("user.dir");
        }
    }

    /**
     * 初始化数据库
     * 使用原生SQL创建表结构，然后通过MyBatis Plus进行数据操作
     */
    private void initializeDatabase() {
        try (Connection conn = getConnection()) {
            createExcelDataTablesIfNotExists(conn);
            logger.info("数据库初始化成功，数据库路径: {}", dbPath);
        } catch (SQLException e) {
            logger.error("数据库初始化失败", e);
        }
    }

    /**
     * 获取数据库连接（用于表结构初始化）
     */
    private Connection getConnection() throws SQLException {
        String url = "jdbc:sqlite:" + dbPath;
        return DriverManager.getConnection(url);
    }

    /**
     * 获取数据库文件路径
     */
    public String getDbPath() {
        return dbPath;
    }

    /**
     * 创建Excel数据相关表（如果不存在）
     */
    private void createExcelDataTablesIfNotExists(Connection conn) throws SQLException {
        // 创建设备检测数据主表
        createDeviceDetectionDataTable(conn);

        // 创建基础字段表
        createDeviceDetectionBasicFieldTable(conn);

        // 创建表头表
        createDeviceDetectionTableHeaderTable(conn);

        // 创建表格数据表
        createDeviceDetectionTableDataTable(conn);
    }

    /**
     * 创建设备检测数据主表
     */
    private void createDeviceDetectionDataTable(Connection conn) throws SQLException {
        String sql = "CREATE TABLE IF NOT EXISTS " + DEVICE_DETECTION_DATA_TABLE + " (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                "device_code TEXT," +
                "template_id INTEGER," +
                "template_name TEXT," +
                "template_code TEXT," +
                "file_name TEXT NOT NULL," +
                "file_path TEXT NOT NULL," +
                "file_size INTEGER," +
                "parse_status INTEGER DEFAULT 0," + // 0-待解析，1-解析成功，2-解析失败
                "parse_message TEXT," +
                "parse_time TIMESTAMP," +
                "total_sheets INTEGER DEFAULT 0," +
                "parsed_sheets INTEGER DEFAULT 0," +
                "basic_fields_count INTEGER DEFAULT 0," +
                "table_rows_count INTEGER DEFAULT 0," +
                "create_by TEXT," +
                "create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                "update_by TEXT," +
                "update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                "remark TEXT" +
                ")";

        try (Statement stmt = conn.createStatement()) {
            stmt.execute(sql);
            logger.info("设备检测数据主表创建成功: {}", DEVICE_DETECTION_DATA_TABLE);
        }
    }

    /**
     * 创建基础字段表
     */
    private void createDeviceDetectionBasicFieldTable(Connection conn) throws SQLException {
        String sql = "CREATE TABLE IF NOT EXISTS " + DEVICE_DETECTION_BASIC_FIELD_TABLE + " (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                "detection_data_id INTEGER NOT NULL," +
                "sheet_id TEXT," +
                "sheet_name TEXT," +
                "field_code TEXT NOT NULL," +
                "field_name TEXT," +
                "field_value TEXT," +
                "label_position TEXT," +
                "value_position TEXT," +
                "field_type TEXT," +
                "label_row_index INTEGER," +
                "label_col_index INTEGER," +
                "value_row_index INTEGER," +
                "value_col_index INTEGER," +
                "create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                "FOREIGN KEY (detection_data_id) REFERENCES " + DEVICE_DETECTION_DATA_TABLE + "(id)" +
                ")";

        try (Statement stmt = conn.createStatement()) {
            stmt.execute(sql);
            logger.info("基础字段表创建成功: {}", DEVICE_DETECTION_BASIC_FIELD_TABLE);
        }
    }

    /**
     * 创建表头表
     */
    private void createDeviceDetectionTableHeaderTable(Connection conn) throws SQLException {
        String sql = "CREATE TABLE IF NOT EXISTS " + DEVICE_DETECTION_TABLE_HEADER_TABLE + " (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                "detection_data_id INTEGER NOT NULL," +
                "sheet_id TEXT," +
                "sheet_name TEXT," +
                "header_code TEXT NOT NULL," +
                "header_name TEXT," +
                "header_position TEXT," +
                "header_col_index INTEGER," +
                "header_row_index INTEGER," +
                "data_type TEXT," +
                "column_order INTEGER," +
                "create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                "FOREIGN KEY (detection_data_id) REFERENCES " + DEVICE_DETECTION_DATA_TABLE + "(id)" +
                ")";

        try (Statement stmt = conn.createStatement()) {
            stmt.execute(sql);
            logger.info("表头表创建成功: {}", DEVICE_DETECTION_TABLE_HEADER_TABLE);
        }
    }

    /**
     * 创建表格数据表
     */
    private void createDeviceDetectionTableDataTable(Connection conn) throws SQLException {
        String sql = "CREATE TABLE IF NOT EXISTS " + DEVICE_DETECTION_TABLE_DATA_TABLE + " (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                "detection_data_id INTEGER NOT NULL," +
                "sheet_id TEXT," +
                "sheet_name TEXT," +
                "row_index INTEGER," +
                "row_data TEXT," +
                "row_order INTEGER," +
                "create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                "FOREIGN KEY (detection_data_id) REFERENCES " + DEVICE_DETECTION_DATA_TABLE + "(id)" +
                ")";

        try (Statement stmt = conn.createStatement()) {
            stmt.execute(sql);
            logger.info("表格数据表创建成功: {}", DEVICE_DETECTION_TABLE_DATA_TABLE);
        }
    }

    /**
     * 保存Excel解析结果到数据库
     * 使用MyBatis Plus进行数据操作
     *
     * @param parsingResult Excel解析结果
     * @return 保存是否成功
     */
    public boolean saveExcelParsingResult(ExcelParsingService.ExcelParsingResult parsingResult) {
        if (parsingResult == null || !parsingResult.isSuccess()) {
            logger.warn("Excel解析结果为空或解析失败，跳过保存");
            return false;
        }

        try {
            // 使用DeviceDetectionDataService进行数据操作
            DeviceDetectionDataService dataService = new DeviceDetectionDataService();

            // 注意：这里需要根据实际的ExcelParsingResult结构来适配
            // 由于ExcelParsingResult可能来自外部依赖，需要转换为MyBatis Plus实体
            logger.info("开始使用MyBatis Plus保存Excel解析结果");

            // TODO: 实现具体的转换和保存逻辑
            // 这里需要根据实际的ExcelParsingResult结构来实现
            logger.warn("saveExcelParsingResult方法需要根据实际的ExcelParsingResult结构来实现");

            return true;

        } catch (Exception e) {
            logger.error("使用MyBatis Plus保存Excel解析结果失败", e);
            return false;
        }
    }

    /**
     * 从文件名中提取设备编码（保留作为工具方法）
     */
    public static String extractDeviceCodeFromFileName(String filePath) {
        try {
            String fileName = new File(filePath).getName();
            // 移除文件扩展名
            int dotIndex = fileName.lastIndexOf('.');
            if (dotIndex > 0) {
                fileName = fileName.substring(0, dotIndex);
            }

            // 简单的设备编码提取逻辑，可以根据实际需求调整
            // 假设文件名格式为：设备编码_其他信息.xlsx
            int underscoreIndex = fileName.indexOf('_');
            if (underscoreIndex > 0) {
                return fileName.substring(0, underscoreIndex);
            }

            return fileName; // 如果没有下划线，直接使用文件名作为设备编码
        } catch (Exception e) {
            return "UNKNOWN";
        }
    }

    /**
     * 获取文件大小（保留作为工具方法）
     */
    public static long getFileSize(String filePath) {
        try {
            File file = new File(filePath);
            return file.exists() ? file.length() : 0;
        } catch (Exception e) {
            return 0;
        }
    }



    /**
     * 查询Excel解析记录
     * 使用MyBatis Plus进行查询
     */
    public List<ExcelDataRecord> getExcelDataRecords(int page, int pageSize) {
        try {
            DeviceDetectionDataService dataService = new DeviceDetectionDataService();

            // 使用MyBatis Plus进行分页查询
            var deviceDataList = dataService.getPageData(page, pageSize);

            List<ExcelDataRecord> records = new ArrayList<>();
            if (deviceDataList != null) {
                for (var deviceData : deviceDataList) {
                    ExcelDataRecord record = convertToExcelDataRecord(deviceData);
                    records.add(record);
                }
            }

            logger.debug("使用MyBatis Plus查询Excel数据记录成功，页码: {}, 页大小: {}, 记录数: {}",
                    page, pageSize, records.size());
            return records;

        } catch (Exception e) {
            logger.error("使用MyBatis Plus查询Excel数据记录失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取Excel数据记录总数
     * 使用MyBatis Plus进行统计
     */
    public int getExcelDataRecordCount() {
        try {
            DeviceDetectionDataService dataService = new DeviceDetectionDataService();
            long count = dataService.getTotalCount();
            return (int) count;

        } catch (Exception e) {
            logger.error("使用MyBatis Plus获取Excel数据记录总数失败", e);
            return 0;
        }
    }

    /**
     * 获取Excel数据记录的详细信息，包括基础字段和表格数据
     * 使用MyBatis Plus进行查询
     */
    public ExcelDataDetail getExcelDataDetail(Long detectionDataId) {
        try {
            DeviceDetectionDataService dataService = new DeviceDetectionDataService();

            // 1. 获取主记录信息
            var deviceData = dataService.getById(detectionDataId);
            if (deviceData == null) {
                logger.warn("未找到ID为{}的Excel数据记录", detectionDataId);
                return null;
            }

            ExcelDataDetail detail = new ExcelDataDetail();
            ExcelDataRecord mainRecord = convertToExcelDataRecord(deviceData);
            detail.setMainRecord(mainRecord);

            // 2. 获取基础字段数据 - 需要实现转换逻辑
            List<BasicFieldData> basicFields = new ArrayList<>();
            // TODO: 实现基础字段数据的查询和转换
            detail.setBasicFields(basicFields);

            // 3. 获取表格数据 - 需要实现转换逻辑
            List<TableDataInfo> tableData = new ArrayList<>();
            // TODO: 实现表格数据的查询和转换
            detail.setTableData(tableData);

            logger.info("使用MyBatis Plus获取Excel数据详情成功，ID: {}, 基础字段数: {}, 表格数据行数: {}",
                    detectionDataId, basicFields.size(), tableData.size());

            return detail;

        } catch (Exception e) {
            logger.error("使用MyBatis Plus获取Excel数据详情失败，ID: {}", detectionDataId, e);
            return null;
        }
    }

    /**
     * 转换DeviceDetectionData实体为ExcelDataRecord
     */
    private ExcelDataRecord convertToExcelDataRecord(Object deviceData) {
        ExcelDataRecord record = new ExcelDataRecord();

        try {
            // 使用反射或者直接转换，这里需要根据实际的DeviceDetectionData结构来实现
            // 由于DeviceDetectionData来自外部依赖，这里提供一个基本的转换框架

            // TODO: 实现具体的字段转换逻辑
            logger.debug("转换DeviceDetectionData实体为ExcelDataRecord");

        } catch (Exception e) {
            logger.error("转换DeviceDetectionData实体失败", e);
        }

        return record;
    }

    /**
     * 获取MyBatis Plus配置实例
     */
    public MyBatisPlusConfig getMyBatisPlusConfig() {
        return myBatisPlusConfig;
    }

    /**
     * Excel数据记录类
     */
    public static class ExcelDataRecord {
        private Long id;
        private String deviceCode;
        private String templateName;
        private String fileName;
        private String filePath;
        private Long fileSize;
        private Integer parseStatus;
        private String parseMessage;
        private LocalDateTime parseTime;
        private Integer totalSheets;
        private Integer parsedSheets;
        private Integer basicFieldsCount;
        private Integer tableRowsCount;
        private LocalDateTime createTime;
        private String remark;

        // Getters and Setters
        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getDeviceCode() {
            return deviceCode;
        }

        public void setDeviceCode(String deviceCode) {
            this.deviceCode = deviceCode;
        }

        public String getTemplateName() {
            return templateName;
        }

        public void setTemplateName(String templateName) {
            this.templateName = templateName;
        }

        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }

        public String getFilePath() {
            return filePath;
        }

        public void setFilePath(String filePath) {
            this.filePath = filePath;
        }

        public Integer getParseStatus() {
            return parseStatus;
        }

        public void setParseStatus(Integer parseStatus) {
            this.parseStatus = parseStatus;
        }

        public String getParseMessage() {
            return parseMessage;
        }

        public void setParseMessage(String parseMessage) {
            this.parseMessage = parseMessage;
        }

        public LocalDateTime getParseTime() {
            return parseTime;
        }

        public void setParseTime(LocalDateTime parseTime) {
            this.parseTime = parseTime;
        }

        public Integer getTotalSheets() {
            return totalSheets;
        }

        public void setTotalSheets(Integer totalSheets) {
            this.totalSheets = totalSheets;
        }

        public Integer getParsedSheets() {
            return parsedSheets;
        }

        public void setParsedSheets(Integer parsedSheets) {
            this.parsedSheets = parsedSheets;
        }

        public Integer getBasicFieldsCount() {
            return basicFieldsCount;
        }

        public void setBasicFieldsCount(Integer basicFieldsCount) {
            this.basicFieldsCount = basicFieldsCount;
        }

        public Integer getTableRowsCount() {
            return tableRowsCount;
        }

        public void setTableRowsCount(Integer tableRowsCount) {
            this.tableRowsCount = tableRowsCount;
        }

        public LocalDateTime getCreateTime() {
            return createTime;
        }

        public void setCreateTime(LocalDateTime createTime) {
            this.createTime = createTime;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public String getParseStatusText() {
            if (parseStatus == null) return "未知";
            switch (parseStatus) {
                case 0:
                    return "待解析";
                case 1:
                    return "解析成功";
                case 2:
                    return "解析失败";
                default:
                    return "未知";
            }
        }

        public Long getFileSize() {
            return fileSize;
        }

        public void setFileSize(Long fileSize) {
            this.fileSize = fileSize;
        }
    }

    /**
     * Excel数据详情类
     */
    public static class ExcelDataDetail {
        private ExcelDataRecord mainRecord;
        private List<BasicFieldData> basicFields;
        private List<TableDataInfo> tableData;

        public ExcelDataRecord getMainRecord() {
            return mainRecord;
        }

        public void setMainRecord(ExcelDataRecord mainRecord) {
            this.mainRecord = mainRecord;
        }

        public List<BasicFieldData> getBasicFields() {
            return basicFields;
        }

        public void setBasicFields(List<BasicFieldData> basicFields) {
            this.basicFields = basicFields;
        }

        public List<TableDataInfo> getTableData() {
            return tableData;
        }

        public void setTableData(List<TableDataInfo> tableData) {
            this.tableData = tableData;
        }
    }

    /**
     * 基础字段数据类
     */
    public static class BasicFieldData {
        private String sheetId;
        private String sheetName;
        private String fieldCode;
        private String fieldName;
        private String fieldValue;
        private String fieldType;
        private Integer rowIndex;
        private Integer colIndex;

        // Getters and Setters
        public String getSheetId() { return sheetId; }
        public void setSheetId(String sheetId) { this.sheetId = sheetId; }

        public String getSheetName() { return sheetName; }
        public void setSheetName(String sheetName) { this.sheetName = sheetName; }

        public String getFieldCode() { return fieldCode; }
        public void setFieldCode(String fieldCode) { this.fieldCode = fieldCode; }

        public String getFieldName() { return fieldName; }
        public void setFieldName(String fieldName) { this.fieldName = fieldName; }

        public String getFieldValue() { return fieldValue; }
        public void setFieldValue(String fieldValue) { this.fieldValue = fieldValue; }

        public String getFieldType() { return fieldType; }
        public void setFieldType(String fieldType) { this.fieldType = fieldType; }

        public Integer getRowIndex() { return rowIndex; }
        public void setRowIndex(Integer rowIndex) { this.rowIndex = rowIndex; }

        public Integer getColIndex() { return colIndex; }
        public void setColIndex(Integer colIndex) { this.colIndex = colIndex; }
    }

    /**
     * 表格数据信息类
     */
    public static class TableDataInfo {
        private String sheetId;
        private String sheetName;
        private Integer rowIndex;
        private String fieldCode;
        private String fieldValue;

        // Getters and Setters
        public String getSheetId() { return sheetId; }
        public void setSheetId(String sheetId) { this.sheetId = sheetId; }

        public String getSheetName() { return sheetName; }
        public void setSheetName(String sheetName) { this.sheetName = sheetName; }

        public Integer getRowIndex() { return rowIndex; }
        public void setRowIndex(Integer rowIndex) { this.rowIndex = rowIndex; }

        public String getFieldCode() { return fieldCode; }
        public void setFieldCode(String fieldCode) { this.fieldCode = fieldCode; }

        public String getFieldValue() { return fieldValue; }
        public void setFieldValue(String fieldValue) { this.fieldValue = fieldValue; }
    }
}
