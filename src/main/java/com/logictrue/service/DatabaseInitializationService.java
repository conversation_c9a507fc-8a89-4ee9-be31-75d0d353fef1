package com.logictrue.service;

import com.logictrue.config.MyBatisPlusConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * 数据库初始化服务类
 * 使用MyBatis Plus配置进行SQLite数据库表结构初始化
 */
public class DatabaseInitializationService {
    private static final Logger logger = LoggerFactory.getLogger(DatabaseInitializationService.class);

    // Excel数据相关表名
    private static final String DEVICE_DETECTION_DATA_TABLE = "device_detection_data";
    private static final String DEVICE_DETECTION_BASIC_FIELD_TABLE = "device_detection_basic_field";
    private static final String DEVICE_DETECTION_TABLE_HEADER_TABLE = "device_detection_table_header";
    private static final String DEVICE_DETECTION_TABLE_DATA_TABLE = "device_detection_table_data";

    private static DatabaseInitializationService instance;
    private MyBatisPlusConfig myBatisPlusConfig;

    private DatabaseInitializationService() {
        this.myBatisPlusConfig = MyBatisPlusConfig.getInstance();
        initializeTables();
    }

    /**
     * 获取单例实例
     */
    public static synchronized DatabaseInitializationService getInstance() {
        if (instance == null) {
            instance = new DatabaseInitializationService();
        }
        return instance;
    }

    /**
     * 初始化数据库表结构
     */
    private void initializeTables() {
        try (Connection conn = myBatisPlusConfig.getDataSource().getConnection()) {
            createExcelDataTablesIfNotExists(conn);
            logger.info("使用MyBatis Plus配置初始化数据库表结构成功");
        } catch (SQLException e) {
            logger.error("使用MyBatis Plus配置初始化数据库表结构失败", e);
            throw new RuntimeException("数据库表结构初始化失败", e);
        }
    }

    /**
     * 创建Excel数据相关表（如果不存在）
     */
    private void createExcelDataTablesIfNotExists(Connection conn) throws SQLException {
        // 创建设备检测数据主表
        createDeviceDetectionDataTable(conn);

        // 创建基础字段表
        createDeviceDetectionBasicFieldTable(conn);

        // 创建表头表
        createDeviceDetectionTableHeaderTable(conn);

        // 创建表格数据表
        createDeviceDetectionTableDataTable(conn);
    }

    /**
     * 创建设备检测数据主表
     */
    private void createDeviceDetectionDataTable(Connection conn) throws SQLException {
        String sql = "CREATE TABLE IF NOT EXISTS " + DEVICE_DETECTION_DATA_TABLE + " (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                "device_code TEXT," +
                "template_id INTEGER," +
                "template_name TEXT," +
                "template_code TEXT," +
                "file_name TEXT NOT NULL," +
                "file_path TEXT NOT NULL," +
                "file_size INTEGER," +
                "parse_status INTEGER DEFAULT 0," + // 0-待解析，1-解析成功，2-解析失败
                "parse_message TEXT," +
                "parse_time TIMESTAMP," +
                "total_sheets INTEGER DEFAULT 0," +
                "parsed_sheets INTEGER DEFAULT 0," +
                "basic_fields_count INTEGER DEFAULT 0," +
                "table_rows_count INTEGER DEFAULT 0," +
                "create_by TEXT," +
                "create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                "update_by TEXT," +
                "update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                "remark TEXT" +
                ")";

        try (Statement stmt = conn.createStatement()) {
            stmt.execute(sql);
            logger.info("设备检测数据主表创建成功: {}", DEVICE_DETECTION_DATA_TABLE);
        }
    }

    /**
     * 创建基础字段表
     */
    private void createDeviceDetectionBasicFieldTable(Connection conn) throws SQLException {
        String sql = "CREATE TABLE IF NOT EXISTS " + DEVICE_DETECTION_BASIC_FIELD_TABLE + " (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                "detection_data_id INTEGER NOT NULL," +
                "sheet_id TEXT," +
                "sheet_name TEXT," +
                "field_code TEXT NOT NULL," +
                "field_name TEXT," +
                "field_value TEXT," +
                "label_position TEXT," +
                "value_position TEXT," +
                "field_type TEXT," +
                "label_row_index INTEGER," +
                "label_col_index INTEGER," +
                "value_row_index INTEGER," +
                "value_col_index INTEGER," +
                "create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                "FOREIGN KEY (detection_data_id) REFERENCES " + DEVICE_DETECTION_DATA_TABLE + "(id)" +
                ")";

        try (Statement stmt = conn.createStatement()) {
            stmt.execute(sql);
            logger.info("基础字段表创建成功: {}", DEVICE_DETECTION_BASIC_FIELD_TABLE);
        }
    }

    /**
     * 创建表头表
     */
    private void createDeviceDetectionTableHeaderTable(Connection conn) throws SQLException {
        String sql = "CREATE TABLE IF NOT EXISTS " + DEVICE_DETECTION_TABLE_HEADER_TABLE + " (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                "detection_data_id INTEGER NOT NULL," +
                "sheet_id TEXT," +
                "sheet_name TEXT," +
                "header_code TEXT NOT NULL," +
                "header_name TEXT," +
                "header_position TEXT," +
                "header_col_index INTEGER," +
                "header_row_index INTEGER," +
                "data_type TEXT," +
                "column_order INTEGER," +
                "create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                "FOREIGN KEY (detection_data_id) REFERENCES " + DEVICE_DETECTION_DATA_TABLE + "(id)" +
                ")";

        try (Statement stmt = conn.createStatement()) {
            stmt.execute(sql);
            logger.info("表头表创建成功: {}", DEVICE_DETECTION_TABLE_HEADER_TABLE);
        }
    }

    /**
     * 创建表格数据表
     */
    private void createDeviceDetectionTableDataTable(Connection conn) throws SQLException {
        String sql = "CREATE TABLE IF NOT EXISTS " + DEVICE_DETECTION_TABLE_DATA_TABLE + " (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                "detection_data_id INTEGER NOT NULL," +
                "sheet_id TEXT," +
                "sheet_name TEXT," +
                "row_index INTEGER," +
                "row_data TEXT," +
                "row_order INTEGER," +
                "create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                "FOREIGN KEY (detection_data_id) REFERENCES " + DEVICE_DETECTION_DATA_TABLE + "(id)" +
                ")";

        try (Statement stmt = conn.createStatement()) {
            stmt.execute(sql);
            logger.info("表格数据表创建成功: {}", DEVICE_DETECTION_TABLE_DATA_TABLE);
        }
    }

    /**
     * 获取MyBatis Plus配置实例
     */
    public MyBatisPlusConfig getMyBatisPlusConfig() {
        return myBatisPlusConfig;
    }
}
