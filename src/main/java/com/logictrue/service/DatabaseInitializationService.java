package com.logictrue.service;

import com.logictrue.config.MyBatisPlusConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * 数据库初始化服务类
 * 使用MyBatis Plus配置进行SQLite数据库表结构初始化
 */
public class DatabaseInitializationService {
    private static final Logger logger = LoggerFactory.getLogger(DatabaseInitializationService.class);

    // Excel数据相关表名
    private static final String DEVICE_DETECTION_DATA_TABLE = "device_detection_data";
    private static final String DEVICE_DETECTION_BASIC_FIELD_TABLE = "device_detection_basic_field";
    private static final String DEVICE_DETECTION_TABLE_HEADER_TABLE = "device_detection_table_header";
    private static final String DEVICE_DETECTION_TABLE_DATA_TABLE = "device_detection_table_data";

    private static DatabaseInitializationService instance;
    private MyBatisPlusConfig myBatisPlusConfig;

    private DatabaseInitializationService() {
        this.myBatisPlusConfig = MyBatisPlusConfig.getInstance();
        initializeTables();
    }

    /**
     * 获取单例实例
     */
    public static synchronized DatabaseInitializationService getInstance() {
        if (instance == null) {
            instance = new DatabaseInitializationService();
        }
        return instance;
    }

    /**
     * 初始化数据库表结构
     */
    private void initializeTables() {
        try (Connection conn = myBatisPlusConfig.getDataSource().getConnection()) {
            createExcelDataTablesIfNotExists(conn);
            logger.info("使用MyBatis Plus配置初始化数据库表结构成功");
        } catch (SQLException e) {
            logger.error("使用MyBatis Plus配置初始化数据库表结构失败", e);
            throw new RuntimeException("数据库表结构初始化失败", e);
        }
    }

    /**
     * 获取动态表创建服务
     */
    public DynamicTableCreationService getDynamicTableService() {
        return dynamicTableService;
    }

    /**
     * 检查并更新表结构
     */
    public void checkAndUpdateTableStructures() {
        if (dynamicTableService != null) {
            logger.info("开始检查并更新表结构");
            // 这里可以添加具体的表结构检查和更新逻辑
            logger.info("表结构检查完成");
        }
    }

    /**
     * 重新创建所有表（基于实体类注解）
     */
    public void recreateAllTables() {
        if (dynamicTableService != null) {
            dynamicTableService.recreateAllTables();
            logger.info("基于实体类注解重新创建所有表完成");
        }
    }

    /**
     * 更新表结构（基于实体类注解）
     */
    public void updateTableStructures() {
        if (dynamicTableService != null) {
            // 这里可以添加批量更新表结构的逻辑
            logger.info("基于实体类注解更新表结构完成");
        }
    }

    /**
     * 获取MyBatis Plus配置实例
     */
    public MyBatisPlusConfig getMyBatisPlusConfig() {
        return myBatisPlusConfig;
    }
}
