package com.logictrue.service;

import com.logictrue.config.MyBatisPlusConfig;
import com.logictrue.iot.entity.DeviceDetectionBasicField;
import com.logictrue.iot.entity.DeviceDetectionData;
import com.logictrue.iot.entity.DeviceDetectionTableData;
import com.logictrue.iot.entity.DeviceDetectionTableHeader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 重构后的数据库服务类
 * 完全使用MyBatis Plus进行SQLite数据库操作
 */
public class DatabaseServiceRefactored {
    private static final Logger logger = LoggerFactory.getLogger(DatabaseServiceRefactored.class);

    private static final String DB_NAME = "data_iot.db";

    private static DatabaseServiceRefactored instance;
    private String dbPath;
    private MyBatisPlusConfig myBatisPlusConfig;
    private DeviceDetectionDataService dataService;
    private DatabaseInitializationService initService;

    private DatabaseServiceRefactored() {
        // 获取jar包同级目录
        String jarDir = getJarDirectory();
        this.dbPath = jarDir + File.separator + DB_NAME;
        
        // 初始化数据库表结构
        this.initService = DatabaseInitializationService.getInstance();
        this.myBatisPlusConfig = initService.getMyBatisPlusConfig();
        
        // 初始化数据服务
        this.dataService = new DeviceDetectionDataService();
        
        logger.info("使用MyBatis Plus重构的数据库服务初始化成功，数据库路径: {}", dbPath);
    }

    /**
     * 获取单例实例
     */
    public static synchronized DatabaseServiceRefactored getInstance() {
        if (instance == null) {
            instance = new DatabaseServiceRefactored();
        }
        return instance;
    }

    /**
     * 获取jar包所在目录
     */
    private String getJarDirectory() {
        try {
            String jarPath = DatabaseServiceRefactored.class.getProtectionDomain()
                    .getCodeSource().getLocation().toURI().getPath();
            File jarFile = new File(jarPath);

            if (jarFile.isFile()) {
                // 运行的是jar包
                return jarFile.getParent();
            } else {
                // 开发环境，使用项目根目录
                return System.getProperty("user.dir");
            }
        } catch (Exception e) {
            logger.error("获取jar包目录失败", e);
            return System.getProperty("user.dir");
        }
    }

    /**
     * 获取数据库文件路径
     */
    public String getDbPath() {
        return dbPath;
    }

    /**
     * 保存Excel解析结果到数据库
     * 使用MyBatis Plus进行数据操作
     *
     * @param deviceCode 设备编码
     * @param templateName 模板名称
     * @param templateCode 模板编码
     * @param filePath 文件路径
     * @param basicFields 基础字段列表
     * @param tableHeaders 表头列表
     * @param tableData 表格数据列表
     * @return 保存是否成功
     */
    public boolean saveExcelParsingResult(String deviceCode, String templateName, String templateCode,
                                        String filePath, List<DeviceDetectionBasicField> basicFields,
                                        List<DeviceDetectionTableHeader> tableHeaders,
                                        List<DeviceDetectionTableData> tableData) {
        try {
            // 1. 创建主记录
            DeviceDetectionData detectionData = new DeviceDetectionData();
            detectionData.setDeviceCode(deviceCode);
            detectionData.setTemplateName(templateName);
            detectionData.setTemplateCode(templateCode);
            detectionData.setFileName(new File(filePath).getName());
            detectionData.setFilePath(filePath);
            detectionData.setFileSize(getFileSize(filePath));
            detectionData.setParseStatus(0); // 待解析
            detectionData.setCreateBy("system");
            detectionData.setRemark("Excel文件解析导入，模板: " + templateCode);

            // 2. 插入主记录并获取ID
            Long detectionDataId = dataService.insertBeforeParsing(detectionData);
            if (detectionDataId == null) {
                logger.error("插入主记录失败");
                return false;
            }

            // 3. 保存基础字段
            if (basicFields != null && !basicFields.isEmpty()) {
                boolean success = dataService.batchInsertBasicFields(detectionDataId, basicFields);
                if (!success) {
                    logger.error("保存基础字段失败");
                    return false;
                }
            }

            // 4. 保存表头数据
            if (tableHeaders != null && !tableHeaders.isEmpty()) {
                boolean success = dataService.batchInsertTableHeaders(detectionDataId, tableHeaders);
                if (!success) {
                    logger.error("保存表头数据失败");
                    return false;
                }
            }

            // 5. 保存表格数据
            if (tableData != null && !tableData.isEmpty()) {
                boolean success = dataService.batchInsertTableData(detectionDataId, tableData);
                if (!success) {
                    logger.error("保存表格数据失败");
                    return false;
                }
            }

            // 6. 更新解析状态
            int totalSheets = 1; // 简化处理
            int parsedSheets = 1;
            int basicFieldsCount = basicFields != null ? basicFields.size() : 0;
            int tableRowsCount = tableData != null ? tableData.size() : 0;

            boolean updateSuccess = dataService.updateAfterParsing(detectionDataId, 1, "解析成功",
                    totalSheets, parsedSheets, basicFieldsCount, tableRowsCount, "system");

            if (updateSuccess) {
                logger.info("使用MyBatis Plus保存Excel解析结果成功，主记录ID: {}", detectionDataId);
                return true;
            } else {
                logger.error("更新解析状态失败");
                return false;
            }

        } catch (Exception e) {
            logger.error("使用MyBatis Plus保存Excel解析结果失败", e);
            return false;
        }
    }

    /**
     * 分页查询Excel解析记录
     */
    public List<ExcelDataRecord> getExcelDataRecords(int page, int pageSize) {
        try {
            var deviceDataList = dataService.getPageData(page, pageSize);
            List<ExcelDataRecord> records = new ArrayList<>();
            
            if (deviceDataList != null) {
                for (var deviceData : deviceDataList) {
                    ExcelDataRecord record = convertToExcelDataRecord(deviceData);
                    records.add(record);
                }
            }

            logger.debug("使用MyBatis Plus查询Excel数据记录成功，页码: {}, 页大小: {}, 记录数: {}", 
                    page, pageSize, records.size());
            return records;

        } catch (Exception e) {
            logger.error("使用MyBatis Plus查询Excel数据记录失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取Excel数据记录总数
     */
    public int getExcelDataRecordCount() {
        try {
            long count = dataService.getTotalCount();
            return (int) count;
        } catch (Exception e) {
            logger.error("使用MyBatis Plus获取Excel数据记录总数失败", e);
            return 0;
        }
    }

    /**
     * 根据ID获取Excel数据详情
     */
    public ExcelDataDetail getExcelDataDetail(Long detectionDataId) {
        try {
            var deviceData = dataService.getById(detectionDataId);
            if (deviceData == null) {
                logger.warn("未找到ID为{}的Excel数据记录", detectionDataId);
                return null;
            }
            
            ExcelDataDetail detail = new ExcelDataDetail();
            ExcelDataRecord mainRecord = convertToExcelDataRecord(deviceData);
            detail.setMainRecord(mainRecord);

            // TODO: 实现基础字段和表格数据的查询
            detail.setBasicFields(new ArrayList<>());
            detail.setTableData(new ArrayList<>());

            logger.info("使用MyBatis Plus获取Excel数据详情成功，ID: {}", detectionDataId);
            return detail;

        } catch (Exception e) {
            logger.error("使用MyBatis Plus获取Excel数据详情失败，ID: {}", detectionDataId, e);
            return null;
        }
    }

    /**
     * 转换DeviceDetectionData实体为ExcelDataRecord
     */
    private ExcelDataRecord convertToExcelDataRecord(Object deviceData) {
        ExcelDataRecord record = new ExcelDataRecord();
        
        // TODO: 实现具体的字段转换逻辑
        // 这里需要根据实际的DeviceDetectionData结构来实现
        logger.debug("转换DeviceDetectionData实体为ExcelDataRecord");
        
        return record;
    }

    /**
     * 获取文件大小
     */
    public static long getFileSize(String filePath) {
        try {
            File file = new File(filePath);
            return file.exists() ? file.length() : 0;
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 从文件名中提取设备编码
     */
    public static String extractDeviceCodeFromFileName(String filePath) {
        try {
            String fileName = new File(filePath).getName();
            int dotIndex = fileName.lastIndexOf('.');
            if (dotIndex > 0) {
                fileName = fileName.substring(0, dotIndex);
            }

            int underscoreIndex = fileName.indexOf('_');
            if (underscoreIndex > 0) {
                return fileName.substring(0, underscoreIndex);
            }

            return fileName;
        } catch (Exception e) {
            return "UNKNOWN";
        }
    }

    /**
     * 获取MyBatis Plus配置实例
     */
    public MyBatisPlusConfig getMyBatisPlusConfig() {
        return myBatisPlusConfig;
    }

    // 内部类定义保持不变...
    public static class ExcelDataRecord {
        private Long id;
        private String deviceCode;
        private String templateName;
        private String fileName;
        private String filePath;
        private Long fileSize;
        private Integer parseStatus;
        private String parseMessage;
        private LocalDateTime parseTime;
        private Integer totalSheets;
        private Integer parsedSheets;
        private Integer basicFieldsCount;
        private Integer tableRowsCount;
        private LocalDateTime createTime;
        private String remark;

        // Getters and Setters...
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        
        public String getDeviceCode() { return deviceCode; }
        public void setDeviceCode(String deviceCode) { this.deviceCode = deviceCode; }
        
        // ... 其他getter/setter方法
    }

    public static class ExcelDataDetail {
        private ExcelDataRecord mainRecord;
        private List<BasicFieldData> basicFields;
        private List<TableDataInfo> tableData;

        // Getters and Setters...
        public ExcelDataRecord getMainRecord() { return mainRecord; }
        public void setMainRecord(ExcelDataRecord mainRecord) { this.mainRecord = mainRecord; }
        
        public List<BasicFieldData> getBasicFields() { return basicFields; }
        public void setBasicFields(List<BasicFieldData> basicFields) { this.basicFields = basicFields; }
        
        public List<TableDataInfo> getTableData() { return tableData; }
        public void setTableData(List<TableDataInfo> tableData) { this.tableData = tableData; }
    }

    public static class BasicFieldData {
        private String sheetId;
        private String sheetName;
        private String fieldCode;
        private String fieldName;
        private String fieldValue;
        private String fieldType;
        private Integer rowIndex;
        private Integer colIndex;

        // Getters and Setters...
    }

    public static class TableDataInfo {
        private String sheetId;
        private String sheetName;
        private Integer rowIndex;
        private String fieldCode;
        private String fieldValue;

        // Getters and Setters...
    }
}
